# MedScan AI - Medical Image Analysis Platform

A Flask-based web application for medical image analysis using AI/ML models. The platform supports multiple types of medical imaging including brain scans, oral health images, Alzheimer's detection, and fracture analysis.

## 🚀 Features

- **Multi-Model Support**: Brain tumor detection, oral disease classification, Alzheimer's detection, and fracture analysis
- **Real-time Analysis**: Fast image processing with confidence scores
- **Multi-language Support**: French, English, and Arabic translations
- **PDF Reports**: Generate comprehensive medical reports
- **Interactive Chat**: AI-powered medical assistant
- **Secure API**: Input validation, rate limiting, and error handling

## 📋 Prerequisites

- Python 3.8+
- pip (Python package manager)
- Virtual environment (recommended)

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd flask-interface-medscan-ai
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r api/requirements.txt
```

4. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env file with your configuration
```

5. **Required Environment Variables**
```bash
GEMINI_API_KEY=your_gemini_api_key_here
SECRET_KEY=your_secret_key_here
FLASK_ENV=development
```

## 🚀 Running the Application

### Development Mode
```bash
cd api
python app.py
```

### Production Mode
```bash
export FLASK_ENV=production
cd api
python app.py
```

The application will be available at `http://localhost:5000`

## 📚 API Documentation

### Endpoints

#### 1. Image Analysis
```http
POST /api/analyze
Content-Type: application/json

{
    "image": "base64_encoded_image",
    "model_type": "brain|oral|alzheimer|fracture"
}
```

#### 2. File Upload Prediction
```http
POST /api/predict
Content-Type: multipart/form-data

file: image_file
```

#### 3. Chat with Medical Assistant
```http
POST /api/chat
Content-Type: application/json

{
    "message": "Your question here",
    "model_type": "brain|oral|alzheimer|fracture",
    "context": "optional_previous_context"
}
```

#### 4. Translation
```http
POST /api/translate
Content-Type: application/json

{
    "data": {...},
    "target_language": "fr|en|ar"
}
```

#### 5. PDF Report Generation
```http
POST /api/generate-pdf
Content-Type: application/json

{
    "data": {...},
    "image": "base64_encoded_image",
    "site_name": "optional_site_name"
}
```

## 🧪 Testing

Run the test suite:
```bash
pytest tests/
```

Run specific test file:
```bash
pytest tests/test_api.py -v
```

## 📁 Project Structure

```
flask-interface-medscan-ai/
├── api/
│   ├── __init__.py
│   ├── app.py                 # Main Flask application
│   ├── config.py              # Configuration management
│   ├── model_loader.py        # Brain tumor model
│   ├── oral_model_loader.py   # Oral disease model
│   ├── alzheimer_model_loader.py # Alzheimer's model
│   ├── fracture_model_loader.py  # Fracture detection model
│   ├── model_manager.py       # Model management
│   ├── gemini_service.py      # AI analysis service
│   ├── translation_service.py # Translation service
│   ├── pdf_generator.py       # PDF report generation
│   ├── validators.py          # Input validation
│   ├── utils.py               # Utility functions
│   └── requirements.txt       # Python dependencies
├── models/                    # ML model files
├── static/                    # Static assets
├── templates/                 # HTML templates
├── tests/                     # Test files
├── uploads/                   # Uploaded files
├── logs/                      # Application logs
├── .env.example              # Environment template
└── README.md                 # This file
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GEMINI_API_KEY` | Google Gemini API key | Required |
| `SECRET_KEY` | Flask secret key | `dev-secret-key` |
| `FLASK_ENV` | Environment mode | `development` |
| `MAX_CONTENT_LENGTH` | Max upload size (bytes) | `16777216` |
| `UPLOAD_FOLDER` | Upload directory | `uploads` |
| `ALLOWED_EXTENSIONS` | Allowed file types | `png,jpg,jpeg,webp` |

### Model Configuration

Place your trained models in the `models/` directory:
- `best_brain_tumor_model.pth` - Brain tumor detection
- `oral_model.pth` - Oral disease classification
- `alzheimer_model.pth` - Alzheimer's detection
- `fracture_classifier_model.h5` - Fracture detection

## 🛡️ Security Features

- Input validation and sanitization
- File type and size restrictions
- Secure filename handling
- Error handling without information leakage
- Environment-based configuration
- CORS protection

## 🐛 Troubleshooting

### Common Issues

1. **Missing API Key Error**
   - Ensure `GEMINI_API_KEY` is set in your `.env` file

2. **Model Loading Errors**
   - Check that model files exist in the `models/` directory
   - Verify model file permissions

3. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r api/requirements.txt`
   - Check Python version compatibility

4. **Memory Issues**
   - Models are loaded on-demand to optimize memory usage
   - Consider using smaller batch sizes for inference

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation
