# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Keras initializer serialization / deserialization.
"""

import sys as _sys

from keras.initializers import deserialize
from keras.initializers import get
from keras.initializers import serialize
from keras.initializers.initializers_v2 import Constant
from keras.initializers.initializers_v2 import Constant as constant
from keras.initializers.initializers_v2 import GlorotNormal
from keras.initializers.initializers_v2 import GlorotNormal as glorot_normal
from keras.initializers.initializers_v2 import GlorotUniform
from keras.initializers.initializers_v2 import GlorotUniform as glorot_uniform
from keras.initializers.initializers_v2 import HeNormal
from keras.initializers.initializers_v2 import HeNormal as he_normal
from keras.initializers.initializers_v2 import HeUniform
from keras.initializers.initializers_v2 import HeUniform as he_uniform
from keras.initializers.initializers_v2 import Identity
from keras.initializers.initializers_v2 import Identity as identity
from keras.initializers.initializers_v2 import Initializer
from keras.initializers.initializers_v2 import LecunNormal
from keras.initializers.initializers_v2 import LecunNormal as lecun_normal
from keras.initializers.initializers_v2 import LecunUniform
from keras.initializers.initializers_v2 import LecunUniform as lecun_uniform
from keras.initializers.initializers_v2 import Ones
from keras.initializers.initializers_v2 import Ones as ones
from keras.initializers.initializers_v2 import Orthogonal
from keras.initializers.initializers_v2 import Orthogonal as orthogonal
from keras.initializers.initializers_v2 import RandomNormal
from keras.initializers.initializers_v2 import RandomNormal as random_normal
from keras.initializers.initializers_v2 import RandomUniform
from keras.initializers.initializers_v2 import RandomUniform as random_uniform
from keras.initializers.initializers_v2 import TruncatedNormal
from keras.initializers.initializers_v2 import TruncatedNormal as truncated_normal
from keras.initializers.initializers_v2 import VarianceScaling
from keras.initializers.initializers_v2 import VarianceScaling as variance_scaling
from keras.initializers.initializers_v2 import Zeros
from keras.initializers.initializers_v2 import Zeros as zeros