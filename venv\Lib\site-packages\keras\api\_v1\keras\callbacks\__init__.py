# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Callbacks: utilities called at certain points during model training.
"""

import sys as _sys

from keras.callbacks import <PERSON><PERSON>ogger
from keras.callbacks import <PERSON><PERSON><PERSON><PERSON>ger
from keras.callbacks import Callback
from keras.callbacks import Callback<PERSON><PERSON>
from keras.callbacks import EarlyStopping
from keras.callbacks import History
from keras.callbacks import LambdaCallback
from keras.callbacks import LearningRateScheduler
from keras.callbacks import ModelCheckpoint
from keras.callbacks import ProgbarLogger
from keras.callbacks import ReduceLROnPlateau
from keras.callbacks import RemoteMonitor
from keras.callbacks import TerminateOnNaN
from keras.callbacks_v1 import TensorBoard
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.callbacks", public_apis=None, deprecation=True,
      has_lite=False)
