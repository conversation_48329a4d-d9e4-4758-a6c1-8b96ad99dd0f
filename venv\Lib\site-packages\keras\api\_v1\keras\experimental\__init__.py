# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.experimental namespace.
"""

import sys as _sys

from keras.feature_column.sequence_feature_column import SequenceFeatures
from keras.optimizers.schedules.learning_rate_schedule import CosineDecay
from keras.optimizers.schedules.learning_rate_schedule import CosineDecayRestarts
from keras.premade_models.linear import LinearModel
from keras.premade_models.wide_deep import WideDeepModel
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.experimental", public_apis=None, deprecation=True,
      has_lite=False)
