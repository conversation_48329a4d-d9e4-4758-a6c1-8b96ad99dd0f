# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""MobileNet v3 models for Keras.
"""

import sys as _sys

from keras.applications.mobilenet_v3 import decode_predictions
from keras.applications.mobilenet_v3 import preprocess_input
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.applications.mobilenet_v3", public_apis=None, deprecation=True,
      has_lite=False)
