import os
import sys
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory, make_response
from werkzeug.utils import secure_filename
from flask_cors import CORS # type: ignore
import base64
from PIL import Image
import io

# Ajuster le chemin pour les importations relatives
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Importer les modules locaux
from api.model_loader import predict_image
from api.gemini_service import get_medical_analysis, chat_with_medical_assistant
from api.translation_service import translate_results
from api.pdf_generator import create_pdf_report

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_MIMETYPE'] = 'application/json;charset=utf-8'
CORS(app)

# Configuration des chemins
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB

# Créer le dossier uploads s'il n'existe pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def home():
    return send_from_directory('../templates', 'index.html')

@app.route('/uploads/<path:filename>')
def serve_upload(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return jsonify({'error': 'Aucun fichier fourni'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Type de fichier non autorisé'}), 400

    try:
        # Générer un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{timestamp}_{secure_filename(file.filename)}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Sauvegarder le fichier
        file.save(filepath)

        # Faire la prédiction
        result = predict_image(filepath)

        return jsonify({
            **result,
            'image_url': f"/uploads/{filename}?t={timestamp}"
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_image():
    if 'image' not in request.json:
        return jsonify({'error': 'No image provided'}), 400

    try:
        # Decode base64 image
        image_data = request.json['image']
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]

        image = Image.open(io.BytesIO(base64.b64decode(image_data)))

        # Get model type from request (default to 'brain')
        model_type = request.json.get('model_type', 'brain')

        # Get prediction from model
        prediction = predict_image(image, model_type=model_type)

        # Ensure proper encoding for all text fields
        for key in prediction:
            if isinstance(prediction[key], str):
                # Ensure proper UTF-8 encoding
                prediction[key] = prediction[key].encode('latin1').decode('utf-8') if is_latin1_encoded(prediction[key]) else prediction[key]

        # Get analysis from Gemini API
        try:
            analysis = get_medical_analysis(prediction)
            # Ensure proper encoding for analysis fields
            for key in analysis:
                if isinstance(analysis[key], str):
                    analysis[key] = analysis[key].encode('latin1').decode('utf-8') if is_latin1_encoded(analysis[key]) else analysis[key]
        except Exception as e:
            print(f"Error getting analysis: {e}")
            analysis = {
                "summary": f"L'analyse a identifié {prediction['class_name']} avec une confiance de {prediction['confidence']}%.",
                "recommendations": "Consultez un professionnel de santé pour une évaluation complète."
            }

        # Combine results
        result = {
            **prediction,
            "analysis": analysis
        }

        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json;charset=utf-8'
        return response
    except Exception as e:
        print(f"Error in analyze_image: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    if 'message' not in request.json:
        return jsonify({'error': 'No message provided'}), 400

    user_message = request.json['message']
    context = request.json.get('context', None)
    model_type = request.json.get('model_type', 'brain')

    response = chat_with_medical_assistant(user_message, context, model_type)

    return jsonify({'response': response})

@app.route('/api/translate', methods=['POST'])
def translate_api():
    """
    API endpoint to translate results to different languages
    """
    if not request.json or 'data' not in request.json or 'target_language' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        target_language = request.json['target_language']

        # Valider la langue cible
        if target_language not in ['fr', 'en', 'ar']:
            return jsonify({'error': 'Unsupported language'}), 400

        # Si la langue est déjà en français et c'est ce qui est demandé, retourner les données telles quelles
        if target_language == 'fr' and data.get('language', 'fr') == 'fr':
            # Assurons-nous que le champ language est correctement défini
            data['language'] = 'fr'
            return jsonify(data)

        # Traduire les résultats
        translated_data = translate_results(data, target_language)

        # Ajouter l'information de langue aux données
        translated_data['language'] = target_language

        # Vérifier l'encodage des données traduites
        for key in translated_data:
            if isinstance(translated_data[key], str):
                # Assurons-nous que les chaînes sont correctement encodées en UTF-8
                if is_latin1_encoded(translated_data[key]):
                    translated_data[key] = translated_data[key].encode('latin1').decode('utf-8')

        # Vérifier également l'encodage des données d'analyse
        if 'analysis' in translated_data:
            for key in translated_data['analysis']:
                if isinstance(translated_data['analysis'][key], str):
                    if is_latin1_encoded(translated_data['analysis'][key]):
                        translated_data['analysis'][key] = translated_data['analysis'][key].encode('latin1').decode('utf-8')

        return jsonify(translated_data)
    except Exception as e:
        print(f"Error in translate_api: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Générer un rapport PDF avec les résultats d'analyse en trois langues
    """
    if not request.json or 'data' not in request.json or 'image' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        image_data = request.json['image']
        site_name = request.json.get('site_name', 'MedScan AI')

        # Générer le PDF
        pdf_data = create_pdf_report(data, image_data, site_name)

        # Créer une réponse avec le PDF
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=rapport_medical_{datetime.now().strftime("%Y%m%d%H%M%S")}.pdf'

        return response
    except Exception as e:
        print(f"Error generating PDF: {e}")
        return jsonify({'error': str(e)}), 500

# Fonction utilitaire pour détecter si une chaîne est encodée en latin1
def is_latin1_encoded(text):
    try:
        return text.encode('latin1').decode('utf-8') != text
    except:
        return False

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
