import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory, make_response
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
from flask_cors import CORS
import base64
from PIL import Image
import io

# Ajuster le chemin pour les importations relatives
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Importer les modules locaux
from api.config import config
from api.model_loader import predict_image
from api.gemini_service import get_medical_analysis, chat_with_medical_assistant
from api.translation_service import translate_results
from api.pdf_generator import create_pdf_report
from api.utils import validate_image, sanitize_filename, log_request

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')

    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # Initialize CORS
    CORS(app, origins=app.config.get('CORS_ORIGINS', '*'))

    # Set up logging
    if not app.debug and not app.testing:
        setup_logging(app)

    return app

def setup_logging(app):
    """Set up application logging"""
    if not os.path.exists('logs'):
        os.mkdir('logs')

    file_handler = logging.FileHandler('logs/app.log')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('MedScan AI startup')

app = create_app()

# Error handlers
@app.errorhandler(RequestEntityTooLarge)
def handle_file_too_large(e):
    return jsonify({'error': 'File too large. Maximum size is 16MB'}), 413

@app.errorhandler(404)
def handle_not_found(e):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def handle_internal_error(e):
    app.logger.error(f'Internal server error: {str(e)}')
    return jsonify({'error': 'Internal server error'}), 500

@app.route('/')
def home():
    return send_from_directory('../templates', 'index.html')

@app.route('/uploads/<path:filename>')
def serve_upload(filename):
    # Sanitize filename for security
    filename = sanitize_filename(filename)
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/predict', methods=['POST'])
def predict():
    log_request('predict')

    if 'file' not in request.files:
        return jsonify({'error': 'Aucun fichier fourni'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    # Validate file type
    from api.utils import allowed_file
    if not allowed_file(file.filename, app.config['ALLOWED_EXTENSIONS']):
        return jsonify({'error': 'Type de fichier non autorisé'}), 400

    try:
        # Read and validate image data
        file_data = file.read()
        is_valid, error_msg = validate_image(file_data, app.config['MAX_CONTENT_LENGTH'])
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{timestamp}_{sanitize_filename(file.filename)}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Save file
        with open(filepath, 'wb') as f:
            f.write(file_data)

        # Make prediction
        result = predict_image(filepath)

        return jsonify({
            **result,
            'image_url': f"/uploads/{filename}?t={timestamp}"
        })

    except Exception as e:
        app.logger.error(f'Error in predict endpoint: {str(e)}')
        return jsonify({'error': 'Error processing image'}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_image():
    log_request('analyze')

    if not request.json or 'image' not in request.json:
        return jsonify({'error': 'No image provided'}), 400

    try:
        # Decode base64 image
        image_data = request.json['image']
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]

        # Validate base64 data
        try:
            decoded_data = base64.b64decode(image_data)
        except Exception:
            return jsonify({'error': 'Invalid base64 image data'}), 400

        # Validate image
        is_valid, error_msg = validate_image(decoded_data)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        image = Image.open(io.BytesIO(decoded_data))

        # Get and validate model type
        model_type = request.json.get('model_type', 'brain')
        from api.utils import validate_model_type
        if not validate_model_type(model_type):
            return jsonify({'error': 'Invalid model type'}), 400

        # Get prediction from model
        prediction = predict_image(image, model_type=model_type)

        # Fix encoding issues
        from api.utils import fix_encoding
        for key in prediction:
            if isinstance(prediction[key], str):
                prediction[key] = fix_encoding(prediction[key])

        # Get analysis from Gemini API
        try:
            analysis = get_medical_analysis(prediction)
            # Fix encoding for analysis fields
            for key in analysis:
                if isinstance(analysis[key], str):
                    analysis[key] = fix_encoding(analysis[key])
        except Exception as e:
            app.logger.error(f"Error getting analysis: {e}")
            analysis = {
                "summary": f"L'analyse a identifié {prediction['class_name']} avec une confiance de {prediction['confidence']}%.",
                "recommendations": "Consultez un professionnel de santé pour une évaluation complète."
            }

        # Combine results
        result = {
            **prediction,
            "analysis": analysis
        }

        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json;charset=utf-8'
        return response
    except Exception as e:
        app.logger.error(f"Error in analyze_image: {e}")
        return jsonify({'error': 'Error analyzing image'}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    if 'message' not in request.json:
        return jsonify({'error': 'No message provided'}), 400

    user_message = request.json['message']
    context = request.json.get('context', None)
    model_type = request.json.get('model_type', 'brain')

    response = chat_with_medical_assistant(user_message, context, model_type)

    return jsonify({'response': response})

@app.route('/api/translate', methods=['POST'])
def translate_api():
    """
    API endpoint to translate results to different languages
    """
    if not request.json or 'data' not in request.json or 'target_language' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        target_language = request.json['target_language']

        # Valider la langue cible
        if target_language not in ['fr', 'en', 'ar']:
            return jsonify({'error': 'Unsupported language'}), 400

        # Si la langue est déjà en français et c'est ce qui est demandé, retourner les données telles quelles
        if target_language == 'fr' and data.get('language', 'fr') == 'fr':
            # Assurons-nous que le champ language est correctement défini
            data['language'] = 'fr'
            return jsonify(data)

        # Traduire les résultats
        translated_data = translate_results(data, target_language)

        # Ajouter l'information de langue aux données
        translated_data['language'] = target_language

        # Vérifier l'encodage des données traduites
        for key in translated_data:
            if isinstance(translated_data[key], str):
                # Assurons-nous que les chaînes sont correctement encodées en UTF-8
                if is_latin1_encoded(translated_data[key]):
                    translated_data[key] = translated_data[key].encode('latin1').decode('utf-8')

        # Vérifier également l'encodage des données d'analyse
        if 'analysis' in translated_data:
            for key in translated_data['analysis']:
                if isinstance(translated_data['analysis'][key], str):
                    if is_latin1_encoded(translated_data['analysis'][key]):
                        translated_data['analysis'][key] = translated_data['analysis'][key].encode('latin1').decode('utf-8')

        return jsonify(translated_data)
    except Exception as e:
        print(f"Error in translate_api: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Générer un rapport PDF avec les résultats d'analyse en trois langues
    """
    if not request.json or 'data' not in request.json or 'image' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        image_data = request.json['image']
        site_name = request.json.get('site_name', 'MedScan AI')

        # Générer le PDF
        pdf_data = create_pdf_report(data, image_data, site_name)

        # Créer une réponse avec le PDF
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=rapport_medical_{datetime.now().strftime("%Y%m%d%H%M%S")}.pdf'

        return response
    except Exception as e:
        print(f"Error generating PDF: {e}")
        return jsonify({'error': str(e)}), 500

# Fonction utilitaire pour détecter si une chaîne est encodée en latin1
def is_latin1_encoded(text):
    try:
        return text.encode('latin1').decode('utf-8') != text
    except:
        return False

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
