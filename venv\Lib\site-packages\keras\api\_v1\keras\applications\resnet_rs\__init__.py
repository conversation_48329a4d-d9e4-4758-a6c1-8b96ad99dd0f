# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""ResNet-RS models for Keras.

Reference:
- [Revisiting ResNets: Improved Training and Scaling Strategies](
    https://arxiv.org/pdf/2103.07579.pdf)

"""

import sys as _sys

from keras.applications.resnet_rs import ResNetRS101
from keras.applications.resnet_rs import ResNetRS152
from keras.applications.resnet_rs import ResNetRS200
from keras.applications.resnet_rs import ResNetRS270
from keras.applications.resnet_rs import ResNetRS350
from keras.applications.resnet_rs import ResNetRS420
from keras.applications.resnet_rs import ResNetRS50
from keras.applications.resnet_rs import decode_predictions
from keras.applications.resnet_rs import preprocess_input
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.applications.resnet_rs", public_apis=None, deprecation=True,
      has_lite=False)
