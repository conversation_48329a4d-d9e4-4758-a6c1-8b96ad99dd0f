Metadata-Version: 2.1
Name: h5py
Version: 3.8.0
Summary: Read and write HDF5 files from Python
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Homepage, https://www.h5py.org/
Project-URL: Source, https://github.com/h5py/h5py
Project-URL: Documentation, https://docs.h5py.org/en/stable/
Project-URL: Release notes, https://docs.h5py.org/en/stable/whatsnew/index.html
Project-URL: Discussion forum, https://forum.hdfgroup.org/c/hdf-tools/h5py
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: Unix
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Cython
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Database
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: numpy (>=1.14.5)

The h5py package provides both a high- and low-level interface to the HDF5
library from Python. The low-level interface is intended to be a complete
wrapping of the HDF5 API, while the high-level component supports  access to
HDF5 files, datasets and groups using established Python and NumPy concepts.

A strong emphasis on automatic conversion between Python (Numpy) datatypes and
data structures and their HDF5 equivalents vastly simplifies the process of
reading and writing data from Python.

Wheels are provided for several popular platforms, with an included copy of
the HDF5 library (usually the latest version when h5py is released).

You can also `build h5py from source
<https://docs.h5py.org/en/stable/build.html#source-installation>`_
with any HDF5 stable release from version 1.8.4 onwards, although naturally new
HDF5 versions released after this version of h5py may not work.
Odd-numbered minor versions of HDF5 (e.g. 1.13) are experimental, and may not
be supported.
