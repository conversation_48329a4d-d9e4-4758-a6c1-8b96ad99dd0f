"""
Utility functions for MedScan AI application
"""
import os
import re
import logging
import io
from datetime import datetime
from typing import Tuple, Optional
from PIL import Image
from werkzeug.utils import secure_filename
from flask import request, current_app

def validate_image(image_data: bytes, max_size: int = 16 * 1024 * 1024) -> Tuple[bool, str]:
    """
    Validate uploaded image data

    Args:
        image_data: Raw image data
        max_size: Maximum allowed file size in bytes

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check file size
        if len(image_data) > max_size:
            return False, f"File size exceeds maximum allowed size of {max_size // (1024*1024)}MB"

        # Try to open and validate image
        image = Image.open(io.BytesIO(image_data))

        # Check image format
        if image.format.lower() not in ['jpeg', 'jpg', 'png', 'webp']:
            return False, "Unsupported image format. Please use JPEG, PNG, or WebP"

        # Check image dimensions (reasonable limits)
        width, height = image.size
        if width > 4096 or height > 4096:
            return False, "Image dimensions too large. Maximum 4096x4096 pixels"

        if width < 32 or height < 32:
            return False, "Image dimensions too small. Minimum 32x32 pixels"

        # Check if image has valid content
        try:
            image.verify()
        except Exception:
            return False, "Invalid or corrupted image file"

        return True, ""

    except Exception as e:
        return False, f"Error validating image: {str(e)}"

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage

    Args:
        filename: Original filename

    Returns:
        Sanitized filename
    """
    # Use werkzeug's secure_filename as base
    filename = secure_filename(filename)

    # Additional sanitization
    filename = re.sub(r'[^\w\-_\.]', '', filename)

    # Ensure filename is not empty
    if not filename:
        filename = "unnamed_file"

    return filename

def log_request(endpoint: str, user_ip: str = None, additional_info: dict = None):
    """
    Log API request for monitoring and debugging

    Args:
        endpoint: API endpoint being accessed
        user_ip: User's IP address
        additional_info: Additional information to log
    """
    if not user_ip:
        user_ip = request.remote_addr

    log_data = {
        'endpoint': endpoint,
        'ip': user_ip,
        'user_agent': request.headers.get('User-Agent', 'Unknown'),
        'timestamp': datetime.now().isoformat()
    }

    if additional_info:
        log_data.update(additional_info)

    current_app.logger.info(f"API Request: {log_data}")

def is_latin1_encoded(text: str) -> bool:
    """
    Check if text is encoded in latin1 and needs UTF-8 conversion

    Args:
        text: Text to check

    Returns:
        True if text appears to be latin1 encoded
    """
    try:
        return text.encode('latin1').decode('utf-8') != text
    except (UnicodeEncodeError, UnicodeDecodeError):
        return False

def fix_encoding(text: str) -> str:
    """
    Fix common encoding issues in text

    Args:
        text: Text to fix

    Returns:
        Fixed text
    """
    if not isinstance(text, str):
        return str(text)

    try:
        # Try to fix latin1 to utf-8 conversion
        if is_latin1_encoded(text):
            return text.encode('latin1').decode('utf-8')
    except (UnicodeEncodeError, UnicodeDecodeError):
        pass

    return text

def validate_model_type(model_type: str) -> bool:
    """
    Validate if model type is supported

    Args:
        model_type: Model type to validate

    Returns:
        True if model type is valid
    """
    valid_types = ['brain', 'oral', 'alzheimer', 'fracture']
    return model_type in valid_types

def get_file_extension(filename: str) -> str:
    """
    Get file extension from filename

    Args:
        filename: Filename to extract extension from

    Returns:
        File extension (lowercase)
    """
    return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

def allowed_file(filename: str, allowed_extensions: set = None) -> bool:
    """
    Check if file extension is allowed

    Args:
        filename: Filename to check
        allowed_extensions: Set of allowed extensions

    Returns:
        True if file is allowed
    """
    if allowed_extensions is None:
        allowed_extensions = {'png', 'jpg', 'jpeg', 'webp'}

    return '.' in filename and get_file_extension(filename) in allowed_extensions