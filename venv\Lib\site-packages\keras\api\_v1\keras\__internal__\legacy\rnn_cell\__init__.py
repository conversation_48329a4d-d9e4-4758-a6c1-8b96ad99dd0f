# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.__internal__.legacy.rnn_cell namespace.
"""

import sys as _sys

from keras.layers.rnn.legacy_cell_wrappers import Device<PERSON>rapper
from keras.layers.rnn.legacy_cell_wrappers import DropoutWrapper
from keras.layers.rnn.legacy_cell_wrappers import ResidualWrapper
from keras.layers.rnn.legacy_cells import BasicLSTMCell
from keras.layers.rnn.legacy_cells import BasicRNNCell
from keras.layers.rnn.legacy_cells import GRUCell
from keras.layers.rnn.legacy_cells import LSTMCell
from keras.layers.rnn.legacy_cells import LSTMStateTuple
from keras.layers.rnn.legacy_cells import MultiRNNCell
from keras.layers.rnn.legacy_cells import RNNCell
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.__internal__.legacy.rnn_cell", public_apis=None, deprecation=True,
      has_lite=False)
