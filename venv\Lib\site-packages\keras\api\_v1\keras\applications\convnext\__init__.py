# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""ConvNeXt models for Keras.

References:

- [A ConvNet for the 2020s](https://arxiv.org/abs/2201.03545)
  (CVPR 2022)

"""

import sys as _sys

from keras.applications.convnext import ConvNeXtBase
from keras.applications.convnext import ConvNeXtLarge
from keras.applications.convnext import ConvNeXtSmall
from keras.applications.convnext import ConvNeXtTiny
from keras.applications.convnext import ConvNeXtXLarge
from keras.applications.convnext import decode_predictions
from keras.applications.convnext import preprocess_input
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.applications.convnext", public_apis=None, deprecation=True,
      has_lite=False)
