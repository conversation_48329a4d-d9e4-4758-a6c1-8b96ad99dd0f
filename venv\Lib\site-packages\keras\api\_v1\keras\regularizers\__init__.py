# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Built-in regularizers.
"""

import sys as _sys

from keras.regularizers import L1
from keras.regularizers import L1 as l1
from keras.regularizers import L1L2
from keras.regularizers import L2
from keras.regularizers import L2 as l2
from keras.regularizers import Regularizer
from keras.regularizers import deserialize
from keras.regularizers import get
from keras.regularizers import l1_l2
from keras.regularizers import serialize
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.regularizers", public_apis=None, deprecation=True,
      has_lite=False)
