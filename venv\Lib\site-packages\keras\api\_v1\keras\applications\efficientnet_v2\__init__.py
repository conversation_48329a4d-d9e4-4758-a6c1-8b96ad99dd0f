# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""EfficientNet V2 models for Keras.

Reference:
- [EfficientNetV2: Smaller Models and Faster Training](
    https://arxiv.org/abs/2104.00298) (ICML 2021)

"""

import sys as _sys

from keras.applications.efficientnet_v2 import EfficientNetV2B0
from keras.applications.efficientnet_v2 import EfficientNetV2B1
from keras.applications.efficientnet_v2 import EfficientNetV2B2
from keras.applications.efficientnet_v2 import EfficientNetV2B3
from keras.applications.efficientnet_v2 import EfficientNetV2L
from keras.applications.efficientnet_v2 import EfficientNetV2M
from keras.applications.efficientnet_v2 import EfficientNetV2S
from keras.applications.efficientnet_v2 import decode_predictions
from keras.applications.efficientnet_v2 import preprocess_input
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.applications.efficientnet_v2", public_apis=None, deprecation=True,
      has_lite=False)
