# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.__internal__.legacy.layers.experimental namespace.
"""

import sys as _sys

from keras.legacy_tf_layers.base import keras_style_scope
from keras.legacy_tf_layers.base import set_keras_style
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.__internal__.legacy.layers.experimental", public_apis=None, deprecation=True,
      has_lite=False)
