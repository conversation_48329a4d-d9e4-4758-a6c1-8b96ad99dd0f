import requests
import os
import json
from typing import Dict, Any, Optional
from langdetect import detect

# Configuration de l'API Gemini
GEMINI_API_KEY = "AIzaSyB9V45g5Ax-Voqjw1J7X61P2gJqkWagS_4"
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={GEMINI_API_KEY}"

# Fallback API (MyMemory) si Gemini échoue
FALLBACK_TRANSLATION_API_URL = os.environ.get('TRANSLATION_API_URL', 'https://api.mymemory.translated.net/get')

# Dictionnaires de traduction pour les termes médicaux courants
# Utilisés comme fallback si l'API de traduction échoue
MEDICAL_TERMS = {
    'fr': {
        'Pas de tumeur': 'Pas de tumeur',
        'Gliome': 'Gliome',
        'Méningiome': 'Méningiome',
        'Tumeur hypophysaire': 'Tumeur hypophysaire',
        'Aucune tumeur cérébrale détectée': 'Aucune tumeur cérébrale détectée',
        'Tumeur gliale détectée': 'Tumeur gliale détectée',
        'Tumeur des méninges détectée': 'Tumeur des méninges détectée',
        'Tumeur de l\'hypophyse détectée': 'Tumeur de l\'hypophyse détectée',
        'confiance': 'confiance',
        'Recommandations': 'Recommandations',
        'Ces résultats sont fournis à titre indicatif': 'Ces résultats sont fournis à titre indicatif',
        'Consultez un professionnel de santé': 'Consultez un professionnel de santé'
    },
    'en': {
        'Pas de tumeur': 'No tumor',
        'Gliome': 'Glioma',
        'Méningiome': 'Meningioma',
        'Tumeur hypophysaire': 'Pituitary tumor',
        'Aucune tumeur cérébrale détectée': 'No brain tumor detected',
        'Tumeur gliale détectée': 'Glial tumor detected',
        'Tumeur des méninges détectée': 'Meninges tumor detected',
        'Tumeur de l\'hypophyse détectée': 'Pituitary gland tumor detected',
        'confiance': 'confidence',
        'Recommandations': 'Recommendations',
        'Ces résultats sont fournis à titre indicatif': 'These results are provided for informational purposes only',
        'Consultez un professionnel de santé': 'Consult a healthcare professional'
    },
    'ar': {
        'Pas de tumeur': 'لا ورم',
        'Gliome': 'ورم دبقي',
        'Méningiome': 'ورم سحائي',
        'Tumeur hypophysaire': 'ورم النخامية',
        'Aucune tumeur cérébrale détectée': 'لم يتم الكشف عن ورم في الدماغ',
        'Tumeur gliale détectée': 'تم الكشف عن ورم دبقي',
        'Tumeur des méninges détectée': 'تم الكشف عن ورم في السحايا',
        'Tumeur de l\'hypophyse détectée': 'تم الكشف عن ورم في الغدة النخامية',
        'confiance': 'الثقة',
        'Recommandations': 'التوصيات',
        'Ces résultats sont fournis à titre indicatif': 'هذه النتائج مقدمة لأغراض إعلامية فقط',
        'Consultez un professionnel de santé': 'استشر أخصائي رعاية صحية'
    }
}

def translate_text_with_gemini(text: str, target_language: str, source_language: str = 'fr', is_medical: bool = False) -> Optional[str]:
    """
    Translate text using Gemini API

    Args:
        text: Text to translate
        target_language: Target language code (en, fr, ar)
        source_language: Source language code (default: fr)
        is_medical: Whether the text is medical content (for specialized translation)

    Returns:
        Translated text
    """
    # Mapper les codes de langue pour Gemini
    language_names = {
        'fr': 'French',
        'en': 'English',
        'ar': 'Arabic'
    }

    source_lang_name = language_names.get(source_language, 'French')
    target_lang_name = language_names.get(target_language, 'English')

    # Créer le prompt pour Gemini
    if is_medical:
        prompt = f"""
        Translate the following medical text from {source_lang_name} to {target_lang_name}.
        This is a medical text related to brain tumor diagnosis.
        Use appropriate medical terminology in the target language.
        Provide only the translated text without any additional explanations or notes.

        Text to translate: "{text}"
        """
    else:
        prompt = f"""
        Translate the following text from {source_lang_name} to {target_lang_name}.
        Provide only the translated text without any additional explanations or notes.

        Text to translate: "{text}"
        """

    # Préparer la requête pour l'API Gemini
    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        # Appel à l'API Gemini
        response = requests.post(GEMINI_API_URL, headers=headers, data=json.dumps(payload))
        response_data = response.json()

        # Extraire la réponse
        translated = response_data["candidates"][0]["content"]["parts"][0]["text"]

        # Nettoyer la réponse (enlever les guillemets si présents)
        translated = translated.strip()
        if translated.startswith('"') and translated.endswith('"'):
            translated = translated[1:-1]

        # Corriger les problèmes d'encodage courants
        translated = fix_encoding(translated)

        return translated
    except Exception as e:
        print(f"Gemini translation error: {e}")
        return None  # Retourner None pour indiquer l'échec et permettre le fallback

def translate_text(text: str, target_language: str, source_language: str = 'fr') -> str:
    """
    Translate text using translation API

    Args:
        text: Text to translate
        target_language: Target language code (en, fr, ar)
        source_language: Source language code (default: fr)

    Returns:
        Translated text
    """
    # Vérifications de base
    if not text or not isinstance(text, str):
        return text if isinstance(text, str) else str(text)

    # Nettoyer le texte avant traduction
    text = text.strip()

    # Si le texte est vide ou ne contient que des caractères spéciaux, retourner tel quel
    if not text or text.isspace() or all(not c.isalpha() for c in text):
        return text

    # Si les langues source et cible sont identiques, retourner le texte tel quel
    if target_language == source_language:
        return text

    # Vérifier si le texte est dans notre dictionnaire de termes médicaux
    if text in MEDICAL_TERMS.get(source_language, {}) and target_language in MEDICAL_TERMS:
        return MEDICAL_TERMS[target_language].get(text, text)

    try:
        # Détection automatique de la langue source si nécessaire
        detected_source = source_language
        if source_language == 'auto':
            # Utiliser la bibliothèque de détection de langue
            try:
                detected_source = detect(text)
                # Mapper les codes de langue si nécessaire
                lang_map = {'fr': 'fr', 'en': 'en', 'ar': 'ar'}
                detected_source = lang_map.get(detected_source, 'en')
            except:
                # En cas d'échec de détection, utiliser l'anglais comme langue par défaut
                detected_source = 'en'

        # Si après détection, les langues source et cible sont identiques, retourner le texte tel quel
        if detected_source == target_language:
            return text

        # Déterminer si le texte est médical
        is_medical = False
        medical_keywords = ['tumeur', 'tumor', 'cancer', 'gliome', 'glioma', 'méningiome', 'meningioma',
                           'hypophyse', 'pituitary', 'cerveau', 'brain', 'IRM', 'MRI', 'diagnostic']

        # Vérifier si le texte contient des termes médicaux
        for keyword in medical_keywords:
            if keyword.lower() in text.lower():
                is_medical = True
                break

        # Essayer d'abord avec Gemini
        gemini_translation = translate_text_with_gemini(text, target_language, detected_source, is_medical)
        if gemini_translation:
            return gemini_translation

        # Fallback: Appel à l'API de traduction MyMemory
        params = {
            'q': text,
            'langpair': f'{detected_source}|{target_language}'
        }

        response = requests.get(FALLBACK_TRANSLATION_API_URL, params=params)
        data = response.json()

        if response.status_code == 200 and data.get('responseStatus') == 200:
            translated = data['responseData']['translatedText']

            # Vérifier si la traduction est valide
            if translated and translated.strip():
                # Corriger les problèmes d'encodage courants
                translated = fix_encoding(translated)
                return translated
            else:
                return text
        else:
            # Fallback: recherche partielle dans le dictionnaire
            for source_term, target_term in MEDICAL_TERMS.get(target_language, {}).items():
                text = text.replace(source_term, target_term)
            return text
    except Exception as e:
        print(f"Translation error: {e}")
        return text

def fix_encoding(text: str) -> str:
    """
    Corriger les problèmes d'encodage courants dans les textes traduits
    """
    # Remplacer les séquences d'échappement HTML courantes
    replacements = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'",
        '&nbsp;': ' ',
        # Problèmes d'encodage UTF-8 courants
        'Ã©': 'é',
        'Ã¨': 'è',
        'Ãª': 'ê',
        'Ã«': 'ë',
        'Ã ': 'à',
        'Ã¢': 'â',
        'Ã®': 'î',
        'Ã¯': 'ï',
        'Ã´': 'ô',
        'Ã¶': 'ö',
        'Ã¹': 'ù',
        'Ã»': 'û',
        'Ã¼': 'ü',
        'Ã§': 'ç'
    }

    for old, new in replacements.items():
        text = text.replace(old, new)

    return text

def translate_medical_analysis(analysis: Dict[str, str], target_language: str, source_language: str = 'fr') -> Dict[str, str]:
    """
    Translate medical analysis results using Gemini API

    Args:
        analysis: Analysis dictionary with summary and recommendations
        target_language: Target language code (en, fr, ar)
        source_language: Source language code (default: fr)

    Returns:
        Dictionary with translated fields
    """
    # Si les langues source et cible sont identiques, retourner les données telles quelles
    if target_language == source_language:
        return analysis

    # Créer une copie des données pour la traduction
    translated_analysis = {}

    # Traduire chaque champ avec Gemini (optimisé pour le contenu médical)
    for key, value in analysis.items():
        if isinstance(value, str):
            translated_analysis[key] = translate_text_with_gemini(value, target_language, source_language, is_medical=True)
        else:
            translated_analysis[key] = value

    return translated_analysis

def translate_results(data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
    """
    Translate all relevant fields in the results data

    Args:
        data: Results data dictionary
        target_language: Target language code (en, fr, ar)

    Returns:
        Dictionary with translated fields
    """
    # Déterminer la langue source
    source_language = data.get('language', 'fr')

    # Si les données sont déjà dans la langue cible, retourner les données telles quelles
    if target_language == source_language:
        # S'assurer que le champ language est correctement défini
        data_copy = data.copy()
        data_copy['language'] = target_language
        return data_copy

    # Créer une copie des données pour la traduction
    translated_data = data.copy()

    # Définir la langue dans les données traduites
    translated_data['language'] = target_language

    # Traiter spécifiquement l'analyse médicale si elle existe
    if 'analysis' in data and isinstance(data['analysis'], dict):
        translated_data['analysis'] = translate_medical_analysis(data['analysis'], target_language, source_language)

    # Fonction pour traduire récursivement les champs d'un dictionnaire
    def translate_dict(d: Dict[str, Any]) -> Dict[str, Any]:
        result = {}
        for key, value in d.items():
            # Ignorer certains champs qui ne doivent pas être traduits
            if key in ['language', 'confidence', 'id', 'timestamp', 'image_url', 'analysis']:
                result[key] = value
                continue

            # Traduire en fonction du type de données
            if isinstance(value, str):
                result[key] = translate_text(value, target_language, source_language)
            elif isinstance(value, list):
                result[key] = [
                    translate_text(item, target_language, source_language) if isinstance(item, str)
                    else translate_dict(item) if isinstance(item, dict)
                    else item
                    for item in value
                ]
            elif isinstance(value, dict):
                result[key] = translate_dict(value)
            else:
                result[key] = value
        return result

    # Traduire tous les champs pertinents (sauf analysis qui a déjà été traité)
    translated_data = translate_dict(translated_data)

    return translated_data



