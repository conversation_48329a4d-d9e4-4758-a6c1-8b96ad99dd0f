# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Small NumPy datasets for debugging/testing.
"""

import sys as _sys

from keras.api.keras.datasets import boston_housing
from keras.api.keras.datasets import cifar10
from keras.api.keras.datasets import cifar100
from keras.api.keras.datasets import fashion_mnist
from keras.api.keras.datasets import imdb
from keras.api.keras.datasets import mnist
from keras.api.keras.datasets import reuters
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.datasets", public_apis=None, deprecation=True,
      has_lite=False)
