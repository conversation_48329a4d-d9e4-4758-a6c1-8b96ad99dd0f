"""
Input validation functions for MedScan AI API
"""
import re
import base64
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from flask import request
from PIL import Image
import io

class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

def validate_json_request(required_fields: list, optional_fields: list = None) -> Dict[str, Any]:
    """
    Validate JSON request data
    
    Args:
        required_fields: List of required field names
        optional_fields: List of optional field names
    
    Returns:
        Validated request data
    
    Raises:
        ValidationError: If validation fails
    """
    if not request.json:
        raise ValidationError("Request must contain JSON data")
    
    data = request.json
    
    # Check required fields
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None:
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")
    
    # Filter allowed fields
    allowed_fields = set(required_fields)
    if optional_fields:
        allowed_fields.update(optional_fields)
    
    validated_data = {}
    for field in allowed_fields:
        if field in data:
            validated_data[field] = data[field]
    
    return validated_data

def validate_base64_image(image_data: str, max_size: int = 16 * 1024 * 1024) -> bytes:
    """
    Validate base64 encoded image data
    
    Args:
        image_data: Base64 encoded image string
        max_size: Maximum allowed file size in bytes
    
    Returns:
        Decoded image bytes
    
    Raises:
        ValidationError: If validation fails
    """
    if not image_data or not isinstance(image_data, str):
        raise ValidationError("Image data must be a non-empty string")
    
    # Remove data URL prefix if present
    if ',' in image_data:
        image_data = image_data.split(',')[1]
    
    # Validate base64 format
    try:
        decoded_data = base64.b64decode(image_data)
    except Exception:
        raise ValidationError("Invalid base64 image data")
    
    # Check file size
    if len(decoded_data) > max_size:
        raise ValidationError(f"Image size exceeds maximum allowed size of {max_size // (1024*1024)}MB")
    
    # Validate image format
    try:
        image = Image.open(io.BytesIO(decoded_data))
        
        # Check image format
        if image.format.lower() not in ['jpeg', 'jpg', 'png', 'webp']:
            raise ValidationError("Unsupported image format. Please use JPEG, PNG, or WebP")
        
        # Check image dimensions
        width, height = image.size
        if width > 4096 or height > 4096:
            raise ValidationError("Image dimensions too large. Maximum 4096x4096 pixels")
        
        if width < 32 or height < 32:
            raise ValidationError("Image dimensions too small. Minimum 32x32 pixels")
        
        # Verify image integrity
        image.verify()
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"Invalid or corrupted image file: {str(e)}")
    
    return decoded_data

def validate_model_type(model_type: str) -> str:
    """
    Validate model type parameter
    
    Args:
        model_type: Model type to validate
    
    Returns:
        Validated model type
    
    Raises:
        ValidationError: If model type is invalid
    """
    if not model_type or not isinstance(model_type, str):
        raise ValidationError("Model type must be a non-empty string")
    
    valid_types = ['brain', 'oral', 'alzheimer', 'fracture']
    model_type = model_type.lower().strip()
    
    if model_type not in valid_types:
        raise ValidationError(f"Invalid model type. Must be one of: {', '.join(valid_types)}")
    
    return model_type

def validate_language_code(language: str) -> str:
    """
    Validate language code parameter
    
    Args:
        language: Language code to validate
    
    Returns:
        Validated language code
    
    Raises:
        ValidationError: If language code is invalid
    """
    if not language or not isinstance(language, str):
        raise ValidationError("Language code must be a non-empty string")
    
    valid_languages = ['fr', 'en', 'ar']
    language = language.lower().strip()
    
    if language not in valid_languages:
        raise ValidationError(f"Invalid language code. Must be one of: {', '.join(valid_languages)}")
    
    return language

def validate_text_input(text: str, max_length: int = 1000, min_length: int = 1) -> str:
    """
    Validate text input
    
    Args:
        text: Text to validate
        max_length: Maximum allowed length
        min_length: Minimum required length
    
    Returns:
        Validated and sanitized text
    
    Raises:
        ValidationError: If text is invalid
    """
    if not isinstance(text, str):
        raise ValidationError("Text input must be a string")
    
    text = text.strip()
    
    if len(text) < min_length:
        raise ValidationError(f"Text must be at least {min_length} characters long")
    
    if len(text) > max_length:
        raise ValidationError(f"Text must not exceed {max_length} characters")
    
    # Basic sanitization - remove potentially harmful characters
    text = re.sub(r'[<>"\']', '', text)
    
    return text

def validate_filename(filename: str) -> str:
    """
    Validate and sanitize filename
    
    Args:
        filename: Filename to validate
    
    Returns:
        Sanitized filename
    
    Raises:
        ValidationError: If filename is invalid
    """
    if not filename or not isinstance(filename, str):
        raise ValidationError("Filename must be a non-empty string")
    
    # Remove path separators and other dangerous characters
    filename = re.sub(r'[/\\:*?"<>|]', '', filename)
    filename = filename.strip()
    
    if not filename:
        raise ValidationError("Filename cannot be empty after sanitization")
    
    # Check file extension
    if '.' not in filename:
        raise ValidationError("Filename must have an extension")
    
    extension = filename.rsplit('.', 1)[1].lower()
    allowed_extensions = {'png', 'jpg', 'jpeg', 'webp'}
    
    if extension not in allowed_extensions:
        raise ValidationError(f"Invalid file extension. Must be one of: {', '.join(allowed_extensions)}")
    
    return filename

def validate_site_name(site_name: str) -> str:
    """
    Validate site name parameter
    
    Args:
        site_name: Site name to validate
    
    Returns:
        Validated site name
    
    Raises:
        ValidationError: If site name is invalid
    """
    if not isinstance(site_name, str):
        raise ValidationError("Site name must be a string")
    
    site_name = site_name.strip()
    
    if len(site_name) > 100:
        raise ValidationError("Site name must not exceed 100 characters")
    
    # Basic sanitization
    site_name = re.sub(r'[<>"\']', '', site_name)
    
    return site_name or "MedScan AI"
