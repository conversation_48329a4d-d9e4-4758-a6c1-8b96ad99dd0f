import os
import torch
import json
from torchvision import transforms
import torch.nn as nn
from PIL import Image
# Chemin absolu sécurisé
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_PATH = os.path.join(BASE_DIR, 'models', 'best_brain_tumor_model.pth')
LABELS_PATH = os.path.join(BASE_DIR, 'models', 'labels.json')

# Import des autres modèles
from api.oral_model_loader import predict_oral_image
from api.alzheimer_model_loader import predict_alzheimer_image
from api.fracture_model_loader import predict_fracture_image

# Vérification des fichiers
if not os.path.exists(MODEL_PATH):
    raise FileNotFoundError(f"Modèle introuvable à l'emplacement: {MODEL_PATH}")

if not os.path.exists(LABELS_PATH):
    raise FileNotFoundError(f"Fichier labels introuvable à l'emplacement: {LABELS_PATH}")

# Définition du modèle (doit correspondre à votre architecture originale)
class BrainTumorCNN(torch.nn.Module):
    def __init__(self, num_classes=4):
        super(BrainTumorCNN, self).__init__()

        self.relu = nn.ReLU()
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)
        self.dropout = nn.Dropout(0.5)

        # Conv Block 1
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)   # (B, 32, 224, 224)

        # Conv Block 2
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)  # (B, 64, 112, 112)

        # Conv Block 3
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1) # (B, 128, 56, 56)

        # Conv Block 4
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1) # (B, 256, 28, 28)

        # Conv Block 5
        self.conv5 = nn.Conv2d(256, 256, kernel_size=3, padding=1) # (B, 256, 14, 14)

        # Final shape after pool: (B, 256, 7, 7)
        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(256 * 7 * 7, 512)
        self.fc2 = nn.Linear(512, num_classes)

    def forward(self, x):
        x = self.pool(self.relu(self.conv1(x)))  # (B, 32, 112, 112)
        x = self.pool(self.relu(self.conv2(x)))  # (B, 64, 56, 56)
        x = self.pool(self.relu(self.conv3(x)))  # (B, 128, 28, 28)
        x = self.pool(self.relu(self.conv4(x)))  # (B, 256, 14, 14)
        x = self.pool(self.relu(self.conv5(x)))  # (B, 256, 7, 7)

        x = self.flatten(x)
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.fc2(x)

        return x
def load_model():
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = BrainTumorCNN(num_classes=4)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    model.eval()
    return model, device

# Charger les labels
with open(LABELS_PATH) as f:
    labels = json.load(f)

# Initialisation
model, device = load_model()
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

def predict_brain_image(pil_image):
    """
    Predict the class of a brain image using the brain tumor model

    Args:
        pil_image: PIL Image object or path to image file

    Returns:
        dict: Prediction results with class, class_name, description, and confidence
    """
    # If pil_image is a string (file path), open the image
    if isinstance(pil_image, str):
        pil_image = Image.open(pil_image).convert('RGB')
    elif not isinstance(pil_image, Image.Image):
        raise TypeError("Input must be a PIL Image or a file path")

    # Ensure image is in RGB mode
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')

    # Apply transformations and get prediction
    image = transform(pil_image).unsqueeze(0).to(device)

    with torch.no_grad():
        outputs = model(image)
        probs = torch.nn.functional.softmax(outputs, dim=1)
        confidence, predicted = torch.max(probs, 1)

    # Get class information
    class_idx = predicted.item()
    class_info = labels.get(str(class_idx), {
        'name': f'Classe {class_idx}',
        'description': 'Aucune description disponible'
    })

    return {
        'class': class_idx,
        'class_name': class_info['name'],
        'description': class_info['description'],
        'confidence': round(confidence.item() * 100, 2),
        'model_type': 'brain'  # Indiquer le type de modèle utilisé
    }

def predict_image(pil_image, model_type='brain'):
    """
    Predict the class of an image using the specified model

    Args:
        pil_image: PIL Image object or path to image file
        model_type: Type of model to use ('brain', 'oral', 'alzheimer', or 'fracture')

    Returns:
        dict: Prediction results with class, class_name, description, and confidence
    """
    if model_type == 'oral':
        return predict_oral_image(pil_image)
    elif model_type == 'alzheimer':
        return predict_alzheimer_image(pil_image)
    elif model_type == 'fracture':
        return predict_fracture_image(pil_image)
    else:
        return predict_brain_image(pil_image)
