import requests
import json

GEMINI_API_KEY = "AIzaSyB9V45g5Ax-Voqjw1J7X61P2gJqkWagS_4"
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={GEMINI_API_KEY}"

def get_medical_analysis(prediction_result):
    """
    Get medical analysis from Gemini API based on classification results

    Args:
        prediction_result (dict): Contains class_name, confidence, etc.

    Returns:
        dict: Contains summary and recommendations
    """
    # Déterminer le type de modèle utilisé
    model_type = prediction_result.get('model_type', 'brain')

    if model_type == 'oral':
        prompt = f"""
        As a dental health AI assistant, analyze the following oral disease classification result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this oral condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    elif model_type == 'alzheimer':
        prompt = f"""
        As a neurology AI assistant specializing in Alzheimer's disease, analyze the following neuroimaging result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this neurological condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    elif model_type == 'fracture':
        prompt = f"""
        As an orthopedic AI assistant specializing in bone fractures, analyze the following X-ray result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this orthopedic condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    else:
        prompt = f"""
        As a medical AI assistant, analyze the following brain MRI scan result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(GEMINI_API_URL, headers=headers, data=json.dumps(payload))
        response_data = response.json()

        # Extract text from response
        text_response = response_data["candidates"][0]["content"]["parts"][0]["text"]

        # Clean the response - remove markdown code blocks if present
        text_response = text_response.replace("```json", "").replace("```", "").strip()

        # Try to parse as JSON
        try:
            # Remove any leading/trailing non-JSON characters
            json_start = text_response.find('{')
            json_end = text_response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                text_response = text_response[json_start:json_end]

            json_response = json.loads(text_response)
            return json_response
        except:
            # If not valid JSON, create our own structure
            return {
                "summary": text_response[:text_response.find("\n\nRecommendations")] if "\n\nRecommendations" in text_response else text_response[:200],
                "recommendations": text_response[text_response.find("\n\nRecommendations")+16:] if "\n\nRecommendations" in text_response else ""
            }

    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return {
            "summary": "Unable to generate analysis at this time.",
            "recommendations": "Please consult with a medical professional for proper diagnosis."
        }

def chat_with_medical_assistant(user_message, context=None, model_type='brain'):
    """
    Chat with Gemini API about medical topics

    Args:
        user_message (str): User's message
        context (str, optional): Previous conversation context
        model_type (str): Type of model ('brain', 'oral', 'alzheimer', or 'fracture')

    Returns:
        str: Assistant's response
    """
    if model_type == 'oral':
        system_prompt = """You are a helpful dental health assistant specializing in oral diseases.
        Provide accurate, helpful information but always remind users to consult dental healthcare professionals.
        Keep responses concise and informative."""
    elif model_type == 'alzheimer':
        system_prompt = """You are a helpful neurology assistant specializing in Alzheimer's disease and cognitive impairments.
        Provide accurate, helpful information but always remind users to consult neurologists and healthcare professionals.
        Keep responses concise and informative."""
    elif model_type == 'fracture':
        system_prompt = """You are a helpful orthopedic assistant specializing in bone fractures and skeletal injuries.
        Provide accurate, helpful information but always remind users to consult orthopedic surgeons and healthcare professionals.
        Keep responses concise and informative."""
    else:
        system_prompt = """You are a helpful medical assistant specializing in brain conditions.
        Provide accurate, helpful information but always remind users to consult healthcare professionals.
        Keep responses concise and informative."""

    full_prompt = f"{system_prompt}\n\n"

    if context:
        full_prompt += f"Previous conversation:\n{context}\n\n"

    full_prompt += f"User: {user_message}\nAssistant:"

    payload = {
        "contents": [{
            "parts": [{
                "text": full_prompt
            }]
        }]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(GEMINI_API_URL, headers=headers, data=json.dumps(payload))
        response_data = response.json()
        return response_data["candidates"][0]["content"]["parts"][0]["text"]
    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return "Je suis désolé, j'ai des difficultés à me connecter en ce moment. Veuillez réessayer plus tard."




