"""
Unit tests for MedScan AI API endpoints
"""
import pytest
import json
import base64
import io
from PIL import Image
import sys
import os

# Add the parent directory to the path so we can import the app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.app import create_app
from api.config import TestingConfig

@pytest.fixture
def app():
    """Create test app instance"""
    app = create_app('testing')
    app.config.from_object(TestingConfig)
    return app

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def sample_image_base64():
    """Create a sample image in base64 format"""
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    img_data = buffer.getvalue()
    return base64.b64encode(img_data).decode('utf-8')

class TestHealthEndpoints:
    """Test basic health and info endpoints"""
    
    def test_home_endpoint(self, client):
        """Test home endpoint returns HTML"""
        response = client.get('/')
        assert response.status_code == 200

class TestPredictionEndpoints:
    """Test prediction and analysis endpoints"""
    
    def test_analyze_missing_image(self, client):
        """Test analyze endpoint with missing image"""
        response = client.post('/api/analyze', 
                             json={},
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_analyze_invalid_base64(self, client):
        """Test analyze endpoint with invalid base64"""
        response = client.post('/api/analyze',
                             json={'image': 'invalid_base64'},
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_analyze_valid_image(self, client, sample_image_base64):
        """Test analyze endpoint with valid image"""
        response = client.post('/api/analyze',
                             json={
                                 'image': sample_image_base64,
                                 'model_type': 'brain'
                             },
                             content_type='application/json')
        # Note: This might fail if models aren't loaded, but structure should be correct
        assert response.status_code in [200, 500]  # 500 if models not available
    
    def test_analyze_invalid_model_type(self, client, sample_image_base64):
        """Test analyze endpoint with invalid model type"""
        response = client.post('/api/analyze',
                             json={
                                 'image': sample_image_base64,
                                 'model_type': 'invalid_model'
                             },
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data

class TestChatEndpoints:
    """Test chat functionality"""
    
    def test_chat_missing_message(self, client):
        """Test chat endpoint with missing message"""
        response = client.post('/api/chat',
                             json={},
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_chat_valid_message(self, client):
        """Test chat endpoint with valid message"""
        response = client.post('/api/chat',
                             json={
                                 'message': 'Hello, what can you tell me about brain tumors?',
                                 'model_type': 'brain'
                             },
                             content_type='application/json')
        # This might fail without API key, but structure should be correct
        assert response.status_code in [200, 500]

class TestTranslationEndpoints:
    """Test translation functionality"""
    
    def test_translate_missing_data(self, client):
        """Test translate endpoint with missing data"""
        response = client.post('/api/translate',
                             json={},
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_translate_invalid_language(self, client):
        """Test translate endpoint with invalid language"""
        response = client.post('/api/translate',
                             json={
                                 'data': {'class_name': 'Test'},
                                 'target_language': 'invalid'
                             },
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data

class TestPDFEndpoints:
    """Test PDF generation functionality"""
    
    def test_generate_pdf_missing_data(self, client):
        """Test PDF generation with missing data"""
        response = client.post('/api/generate-pdf',
                             json={},
                             content_type='application/json')
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data

class TestErrorHandling:
    """Test error handling"""
    
    def test_404_error(self, client):
        """Test 404 error handling"""
        response = client.get('/nonexistent-endpoint')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_method_not_allowed(self, client):
        """Test method not allowed error"""
        response = client.get('/api/analyze')  # Should be POST
        assert response.status_code == 405

if __name__ == '__main__':
    pytest.main([__file__])
