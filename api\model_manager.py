"""
Model Manager for efficient loading and management of ML models
"""
import os
import logging
from typing import Dict, Optional, Any
import threading
from contextlib import contextmanager

class ModelManager:
    """
    Singleton class to manage ML model loading and caching
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        self._models = {}
        self._model_locks = {}
        self._logger = logging.getLogger(__name__)
        self._initialized = True
    
    def get_model(self, model_type: str):
        """
        Get a model instance, loading it if necessary
        
        Args:
            model_type: Type of model ('brain', 'oral', 'alzheimer', 'fracture')
        
        Returns:
            Model instance
        """
        if model_type not in self._model_locks:
            self._model_locks[model_type] = threading.Lock()
        
        with self._model_locks[model_type]:
            if model_type not in self._models:
                self._load_model(model_type)
            
            return self._models.get(model_type)
    
    def _load_model(self, model_type: str):
        """
        Load a specific model type
        
        Args:
            model_type: Type of model to load
        """
        try:
            self._logger.info(f"Loading {model_type} model...")
            
            if model_type == 'brain':
                from api.model_loader import load_model
                model, device = load_model()
                self._models[model_type] = {'model': model, 'device': device}
            
            elif model_type == 'oral':
                from api.oral_model_loader import load_oral_model
                model, device = load_oral_model()
                self._models[model_type] = {'model': model, 'device': device}
            
            elif model_type == 'alzheimer':
                from api.alzheimer_model_loader import load_alzheimer_model
                model, device = load_alzheimer_model()
                self._models[model_type] = {'model': model, 'device': device}
            
            elif model_type == 'fracture':
                from api.fracture_model_loader import load_fracture_model
                model = load_fracture_model()
                self._models[model_type] = {'model': model, 'device': None}
            
            else:
                raise ValueError(f"Unknown model type: {model_type}")
            
            self._logger.info(f"{model_type} model loaded successfully")
            
        except Exception as e:
            self._logger.error(f"Error loading {model_type} model: {e}")
            raise
    
    def unload_model(self, model_type: str):
        """
        Unload a specific model to free memory
        
        Args:
            model_type: Type of model to unload
        """
        if model_type in self._model_locks:
            with self._model_locks[model_type]:
                if model_type in self._models:
                    del self._models[model_type]
                    self._logger.info(f"{model_type} model unloaded")
    
    def unload_all_models(self):
        """
        Unload all models to free memory
        """
        for model_type in list(self._models.keys()):
            self.unload_model(model_type)
    
    def get_loaded_models(self) -> list:
        """
        Get list of currently loaded models
        
        Returns:
            List of loaded model types
        """
        return list(self._models.keys())
    
    @contextmanager
    def model_context(self, model_type: str):
        """
        Context manager for model usage
        
        Args:
            model_type: Type of model to use
        
        Yields:
            Model instance
        """
        model_data = self.get_model(model_type)
        try:
            yield model_data
        finally:
            # Could implement cleanup logic here if needed
            pass

# Global model manager instance
model_manager = ModelManager()

def get_model_manager() -> ModelManager:
    """
    Get the global model manager instance
    
    Returns:
        ModelManager instance
    """
    return model_manager
