# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""RegNet models for Keras.

References:

- [Designing Network Design Spaces](https://arxiv.org/abs/2003.13678)
  (CVPR 2020)
- [Fast and Accurate Model Scaling](https://arxiv.org/abs/2103.06877)
  (CVPR 2021)

"""

import sys as _sys

from keras.applications.regnet import RegNetX002
from keras.applications.regnet import RegNetX004
from keras.applications.regnet import RegNetX006
from keras.applications.regnet import RegNetX008
from keras.applications.regnet import RegNetX016
from keras.applications.regnet import RegNetX032
from keras.applications.regnet import RegNetX040
from keras.applications.regnet import RegNetX064
from keras.applications.regnet import RegNetX080
from keras.applications.regnet import RegNetX120
from keras.applications.regnet import RegNetX160
from keras.applications.regnet import RegNetX320
from keras.applications.regnet import RegNetY002
from keras.applications.regnet import RegNetY004
from keras.applications.regnet import RegNetY006
from keras.applications.regnet import RegNetY008
from keras.applications.regnet import RegNetY016
from keras.applications.regnet import RegNetY032
from keras.applications.regnet import RegNetY040
from keras.applications.regnet import RegNetY064
from keras.applications.regnet import RegNetY080
from keras.applications.regnet import RegNetY120
from keras.applications.regnet import RegNetY160
from keras.applications.regnet import RegNetY320
from keras.applications.regnet import decode_predictions
from keras.applications.regnet import preprocess_input