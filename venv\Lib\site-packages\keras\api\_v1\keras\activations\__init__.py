# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Built-in activation functions.
"""

import sys as _sys

from keras.activations import deserialize
from keras.activations import elu
from keras.activations import exponential
from keras.activations import get
from keras.activations import hard_sigmoid
from keras.activations import linear
from keras.activations import relu
from keras.activations import selu
from keras.activations import serialize
from keras.activations import sigmoid
from keras.activations import softmax
from keras.activations import softplus
from keras.activations import softsign
from keras.activations import swish
from keras.activations import tanh
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.activations", public_apis=None, deprecation=True,
      has_lite=False)
