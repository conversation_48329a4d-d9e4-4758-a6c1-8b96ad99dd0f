# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""All Keras metrics.
"""

import sys as _sys

from keras.losses import binary_crossentropy
from keras.losses import binary_focal_crossentropy
from keras.losses import categorical_crossentropy
from keras.losses import cosine_similarity as cosine
from keras.losses import cosine_similarity as cosine_proximity
from keras.losses import hinge
from keras.losses import kl_divergence
from keras.losses import kl_divergence as KLD
from keras.losses import kl_divergence as kld
from keras.losses import kl_divergence as kullback_leibler_divergence
from keras.losses import log_cosh
from keras.losses import log_cosh as logcosh
from keras.losses import mean_absolute_error
from keras.losses import mean_absolute_error as MAE
from keras.losses import mean_absolute_error as mae
from keras.losses import mean_absolute_percentage_error
from keras.losses import mean_absolute_percentage_error as MAPE
from keras.losses import mean_absolute_percentage_error as mape
from keras.losses import mean_squared_error
from keras.losses import mean_squared_error as MSE
from keras.losses import mean_squared_error as mse
from keras.losses import mean_squared_logarithmic_error
from keras.losses import mean_squared_logarithmic_error as MSLE
from keras.losses import mean_squared_logarithmic_error as msle
from keras.losses import poisson
from keras.losses import sparse_categorical_crossentropy
from keras.losses import squared_hinge
from keras.metrics import deserialize
from keras.metrics import get
from keras.metrics import serialize
from keras.metrics.base_metric import Mean
from keras.metrics.base_metric import MeanMetricWrapper
from keras.metrics.base_metric import MeanTensor
from keras.metrics.base_metric import Metric
from keras.metrics.base_metric import Sum
from keras.metrics.metrics import AUC
from keras.metrics.metrics import Accuracy
from keras.metrics.metrics import BinaryAccuracy
from keras.metrics.metrics import BinaryCrossentropy
from keras.metrics.metrics import BinaryIoU
from keras.metrics.metrics import CategoricalAccuracy
from keras.metrics.metrics import CategoricalCrossentropy
from keras.metrics.metrics import CategoricalHinge
from keras.metrics.metrics import CosineSimilarity
from keras.metrics.metrics import FalseNegatives
from keras.metrics.metrics import FalsePositives
from keras.metrics.metrics import Hinge
from keras.metrics.metrics import IoU
from keras.metrics.metrics import KLDivergence
from keras.metrics.metrics import LogCoshError
from keras.metrics.metrics import MeanAbsoluteError
from keras.metrics.metrics import MeanAbsolutePercentageError
from keras.metrics.metrics import MeanIoU
from keras.metrics.metrics import MeanRelativeError
from keras.metrics.metrics import MeanSquaredError
from keras.metrics.metrics import MeanSquaredLogarithmicError
from keras.metrics.metrics import OneHotIoU
from keras.metrics.metrics import OneHotMeanIoU
from keras.metrics.metrics import Poisson
from keras.metrics.metrics import Precision
from keras.metrics.metrics import PrecisionAtRecall
from keras.metrics.metrics import Recall
from keras.metrics.metrics import RecallAtPrecision
from keras.metrics.metrics import RootMeanSquaredError
from keras.metrics.metrics import SensitivityAtSpecificity
from keras.metrics.metrics import SparseCategoricalAccuracy
from keras.metrics.metrics import SparseCategoricalCrossentropy
from keras.metrics.metrics import SparseTopKCategoricalAccuracy
from keras.metrics.metrics import SpecificityAtSensitivity
from keras.metrics.metrics import SquaredHinge
from keras.metrics.metrics import TopKCategoricalAccuracy
from keras.metrics.metrics import TrueNegatives
from keras.metrics.metrics import TruePositives
from keras.metrics.metrics import binary_accuracy
from keras.metrics.metrics import categorical_accuracy
from keras.metrics.metrics import sparse_categorical_accuracy
from keras.metrics.metrics import sparse_top_k_categorical_accuracy
from keras.metrics.metrics import top_k_categorical_accuracy
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.metrics", public_apis=None, deprecation=True,
      has_lite=False)
