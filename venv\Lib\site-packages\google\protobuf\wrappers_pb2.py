# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/wrappers.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1egoogle/protobuf/wrappers.proto\x12\x0fgoogle.protobuf\"\x1c\n\x0b\x44oubleValue\x12\r\n\x05value\x18\x01 \x01(\x01\"\x1b\n\nFloatValue\x12\r\n\x05value\x18\x01 \x01(\x02\"\x1b\n\nInt64Value\x12\r\n\x05value\x18\x01 \x01(\x03\"\x1c\n\x0bUInt64Value\x12\r\n\x05value\x18\x01 \x01(\x04\"\x1b\n\nInt32Value\x12\r\n\x05value\x18\x01 \x01(\x05\"\x1c\n\x0bUInt32Value\x12\r\n\x05value\x18\x01 \x01(\r\"\x1a\n\tBoolValue\x12\r\n\x05value\x18\x01 \x01(\x08\"\x1c\n\x0bStringValue\x12\r\n\x05value\x18\x01 \x01(\t\"\x1b\n\nBytesValue\x12\r\n\x05value\x18\x01 \x01(\x0c\x42\x83\x01\n\x13\x63om.google.protobufB\rWrappersProtoP\x01Z1google.golang.org/protobuf/types/known/wrapperspb\xf8\x01\x01\xa2\x02\x03GPB\xaa\x02\x1eGoogle.Protobuf.WellKnownTypesb\x06proto3')



_DOUBLEVALUE = DESCRIPTOR.message_types_by_name['DoubleValue']
_FLOATVALUE = DESCRIPTOR.message_types_by_name['FloatValue']
_INT64VALUE = DESCRIPTOR.message_types_by_name['Int64Value']
_UINT64VALUE = DESCRIPTOR.message_types_by_name['UInt64Value']
_INT32VALUE = DESCRIPTOR.message_types_by_name['Int32Value']
_UINT32VALUE = DESCRIPTOR.message_types_by_name['UInt32Value']
_BOOLVALUE = DESCRIPTOR.message_types_by_name['BoolValue']
_STRINGVALUE = DESCRIPTOR.message_types_by_name['StringValue']
_BYTESVALUE = DESCRIPTOR.message_types_by_name['BytesValue']
DoubleValue = _reflection.GeneratedProtocolMessageType('DoubleValue', (_message.Message,), {
  'DESCRIPTOR' : _DOUBLEVALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.DoubleValue)
  })
_sym_db.RegisterMessage(DoubleValue)

FloatValue = _reflection.GeneratedProtocolMessageType('FloatValue', (_message.Message,), {
  'DESCRIPTOR' : _FLOATVALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.FloatValue)
  })
_sym_db.RegisterMessage(FloatValue)

Int64Value = _reflection.GeneratedProtocolMessageType('Int64Value', (_message.Message,), {
  'DESCRIPTOR' : _INT64VALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.Int64Value)
  })
_sym_db.RegisterMessage(Int64Value)

UInt64Value = _reflection.GeneratedProtocolMessageType('UInt64Value', (_message.Message,), {
  'DESCRIPTOR' : _UINT64VALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.UInt64Value)
  })
_sym_db.RegisterMessage(UInt64Value)

Int32Value = _reflection.GeneratedProtocolMessageType('Int32Value', (_message.Message,), {
  'DESCRIPTOR' : _INT32VALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.Int32Value)
  })
_sym_db.RegisterMessage(Int32Value)

UInt32Value = _reflection.GeneratedProtocolMessageType('UInt32Value', (_message.Message,), {
  'DESCRIPTOR' : _UINT32VALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.UInt32Value)
  })
_sym_db.RegisterMessage(UInt32Value)

BoolValue = _reflection.GeneratedProtocolMessageType('BoolValue', (_message.Message,), {
  'DESCRIPTOR' : _BOOLVALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.BoolValue)
  })
_sym_db.RegisterMessage(BoolValue)

StringValue = _reflection.GeneratedProtocolMessageType('StringValue', (_message.Message,), {
  'DESCRIPTOR' : _STRINGVALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.StringValue)
  })
_sym_db.RegisterMessage(StringValue)

BytesValue = _reflection.GeneratedProtocolMessageType('BytesValue', (_message.Message,), {
  'DESCRIPTOR' : _BYTESVALUE,
  '__module__' : 'google.protobuf.wrappers_pb2'
  # @@protoc_insertion_point(class_scope:google.protobuf.BytesValue)
  })
_sym_db.RegisterMessage(BytesValue)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\023com.google.protobufB\rWrappersProtoP\001Z1google.golang.org/protobuf/types/known/wrapperspb\370\001\001\242\002\003GPB\252\002\036Google.Protobuf.WellKnownTypes'
  _DOUBLEVALUE._serialized_start=51
  _DOUBLEVALUE._serialized_end=79
  _FLOATVALUE._serialized_start=81
  _FLOATVALUE._serialized_end=108
  _INT64VALUE._serialized_start=110
  _INT64VALUE._serialized_end=137
  _UINT64VALUE._serialized_start=139
  _UINT64VALUE._serialized_end=167
  _INT32VALUE._serialized_start=169
  _INT32VALUE._serialized_end=196
  _UINT32VALUE._serialized_start=198
  _UINT32VALUE._serialized_end=226
  _BOOLVALUE._serialized_start=228
  _BOOLVALUE._serialized_end=254
  _STRINGVALUE._serialized_start=256
  _STRINGVALUE._serialized_end=284
  _BYTESVALUE._serialized_start=286
  _BYTESVALUE._serialized_end=313
# @@protoc_insertion_point(module_scope)
