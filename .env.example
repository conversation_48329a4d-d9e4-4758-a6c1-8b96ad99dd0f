# Environment Configuration for MedScan AI
# Copy this file to .env and fill in your actual values

# Gemini API Configuration
GEMINI_API_KEY=AIzaSyB9V45g5Ax-Voqjw1J7X61P2gJqkWagS_4

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes
UPLOAD_FOLDER=uploads

# Model Configuration
MODELS_PATH=models

# Security Configuration
ALLOWED_EXTENSIONS=png,jpg,jpeg,webp
CORS_ORIGINS=*

# Database Configuration (if needed in future)
# DATABASE_URL=sqlite:///medscan.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
