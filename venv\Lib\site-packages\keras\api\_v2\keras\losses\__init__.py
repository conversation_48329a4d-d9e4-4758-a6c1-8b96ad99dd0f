# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Built-in loss functions.
"""

import sys as _sys

from keras.losses import BinaryCrossentropy
from keras.losses import BinaryFocalCrossentropy
from keras.losses import CategoricalCrossentropy
from keras.losses import CategoricalHinge
from keras.losses import CosineSimilarity
from keras.losses import Hinge
from keras.losses import Huber
from keras.losses import KLDivergence
from keras.losses import LogCosh
from keras.losses import Loss
from keras.losses import MeanAbsoluteError
from keras.losses import MeanAbsolutePercentageError
from keras.losses import MeanSquaredError
from keras.losses import MeanSquaredLogarithmicError
from keras.losses import Poisson
from keras.losses import SparseCategoricalCrossentropy
from keras.losses import SquaredHinge
from keras.losses import binary_crossentropy
from keras.losses import binary_focal_crossentropy
from keras.losses import categorical_crossentropy
from keras.losses import categorical_hinge
from keras.losses import cosine_similarity
from keras.losses import deserialize
from keras.losses import get
from keras.losses import hinge
from keras.losses import huber
from keras.losses import kl_divergence
from keras.losses import kl_divergence as KLD
from keras.losses import kl_divergence as kld
from keras.losses import kl_divergence as kullback_leibler_divergence
from keras.losses import log_cosh
from keras.losses import log_cosh as logcosh
from keras.losses import mean_absolute_error
from keras.losses import mean_absolute_error as MAE
from keras.losses import mean_absolute_error as mae
from keras.losses import mean_absolute_percentage_error
from keras.losses import mean_absolute_percentage_error as MAPE
from keras.losses import mean_absolute_percentage_error as mape
from keras.losses import mean_squared_error
from keras.losses import mean_squared_error as MSE
from keras.losses import mean_squared_error as mse
from keras.losses import mean_squared_logarithmic_error
from keras.losses import mean_squared_logarithmic_error as MSLE
from keras.losses import mean_squared_logarithmic_error as msle
from keras.losses import poisson
from keras.losses import serialize
from keras.losses import sparse_categorical_crossentropy
from keras.losses import squared_hinge
from keras.utils.losses_utils import ReductionV2 as Reduction