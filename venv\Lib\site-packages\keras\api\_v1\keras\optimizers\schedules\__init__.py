# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Public API for tf.keras.optimizers.schedules namespace.
"""

import sys as _sys

from keras.optimizers.schedules.learning_rate_schedule import CosineDecay
from keras.optimizers.schedules.learning_rate_schedule import CosineDecayRestarts
from keras.optimizers.schedules.learning_rate_schedule import ExponentialDecay
from keras.optimizers.schedules.learning_rate_schedule import InverseTimeDecay
from keras.optimizers.schedules.learning_rate_schedule import LearningRateSchedule
from keras.optimizers.schedules.learning_rate_schedule import PiecewiseConstantDecay
from keras.optimizers.schedules.learning_rate_schedule import PolynomialDecay
from keras.optimizers.schedules.learning_rate_schedule import deserialize
from keras.optimizers.schedules.learning_rate_schedule import serialize
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.optimizers.schedules", public_apis=None, deprecation=True,
      has_lite=False)
