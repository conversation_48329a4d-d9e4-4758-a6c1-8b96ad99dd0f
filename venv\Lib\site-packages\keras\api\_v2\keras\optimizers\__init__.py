# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Built-in optimizer classes.

For more examples see the base class `tf.keras.optimizers.Optimizer`.

"""

import sys as _sys

from keras.api._v2.keras.optimizers import experimental
from keras.api._v2.keras.optimizers import legacy
from keras.api._v2.keras.optimizers import schedules
from keras.optimizers import deserialize
from keras.optimizers import get
from keras.optimizers import serialize
from keras.optimizers.optimizer_experimental.adadelta import Adadelta
from keras.optimizers.optimizer_experimental.adagrad import Adagrad
from keras.optimizers.optimizer_experimental.adam import Adam
from keras.optimizers.optimizer_experimental.adamax import Adamax
from keras.optimizers.optimizer_experimental.ftrl import Ftrl
from keras.optimizers.optimizer_experimental.nadam import Nadam
from keras.optimizers.optimizer_experimental.optimizer import Optimizer
from keras.optimizers.optimizer_experimental.rmsprop import RMSprop
from keras.optimizers.optimizer_experimental.sgd import SGD